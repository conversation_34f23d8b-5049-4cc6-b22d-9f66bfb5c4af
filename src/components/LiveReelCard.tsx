import { useRef, useEffect, useState } from "react";
import { LiveReel } from "@/lib/types";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Eye, Users, TrendingUp, Gavel } from "lucide-react";
import { cn } from "@/lib/utils";
import { timeSyncService } from "@/lib/timeSync";

interface LiveReelCardProps {
  reel: LiveReel;
  onClick: () => void;
  className?: string;
  isInFocus?: boolean;
}

export function LiveReelCard({
  reel,
  onClick,
  className,
  isInFocus = false,
}: LiveReelCardProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [showVideo, setShowVideo] = useState(true);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Update timer every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatTimeRemaining = (endTime: Date) => {
    const now = timeSyncService.isSynchronized()
      ? timeSyncService.getServerTime()
      : currentTime;
    const diff = endTime.getTime() - now;

    if (diff <= 0) {
      return "Ended";
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  // Auto-play when in focus
  useEffect(() => {
    if (videoRef.current) {
      if (isInFocus) {
        videoRef.current
          .play()
          .then(() => {
            setIsPlaying(true);
          })
          .catch(() => {
            console.log("Autoplay failed");
          });
      } else {
        videoRef.current.pause();
        setIsPlaying(false);
      }
    }
  }, [isInFocus]);

  const handleBidClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick();
  };

  const handleVideoEnded = () => {
    setIsPlaying(false);
    if (videoRef.current) {
      videoRef.current.currentTime = 0;
      if (isInFocus) {
        videoRef.current
          .play()
          .then(() => {
            setIsPlaying(true);
          })
          .catch(() => {
            console.log("Video restart failed");
          });
      }
    }
  };

  return (
    <Card
      className={cn(
        "group cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02] border-0 bg-gradient-to-br from-slate-50 to-slate-100 rounded-xl w-72 max-w-72",
        isInFocus && "ring-2 ring-blue-500 scale-[1.02] shadow-lg",
        className,
      )}
      onClick={onClick}
    >
      <div className="relative">
        {/* Compact vertical layout - 9:16 aspect ratio */}
        <div className="aspect-[9/16] overflow-hidden rounded-xl">
          {/* Video */}
          <video
            ref={videoRef}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            poster={reel.thumbnailUrl}
            loop
            playsInline
            onEnded={handleVideoEnded}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          >
            <source src={reel.videoUrl} type="video/mp4" />
            <img
              src={reel.thumbnailUrl}
              alt={reel.title}
              className="w-full h-full object-cover"
            />
          </video>

          {/* Gradient overlays */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/75 via-transparent to-black/30" />
          <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/90 to-transparent" />
        </div>

        {/* Live indicator - smaller */}
        {reel.isLive && (
          <div className="absolute top-2 left-2 flex items-center gap-1 bg-red-500 text-white px-2 py-0.5 rounded-full text-xs font-semibold shadow-md z-10">
            <div className="w-1 h-1 bg-white rounded-full animate-pulse" />
            LIVE
          </div>
        )}

        {/* Viewer count - smaller */}
        <div className="absolute top-2 right-2 flex items-center gap-1 bg-black/50 backdrop-blur-sm text-white px-2 py-0.5 rounded-full text-xs z-10">
          <Eye className="w-3 h-3" />
          {reel.viewerCount > 1000
            ? `${(reel.viewerCount / 1000).toFixed(1)}K`
            : reel.viewerCount.toLocaleString()
          }
        </div>

        {/* Right side action buttons - compact */}
        <div className="absolute right-2 bottom-16 flex flex-col gap-2 z-10">
          {/* Bid button - smaller */}
          <div className="flex flex-col items-center">
            <Button
              onClick={handleBidClick}
              size="sm"
              className="w-8 h-8 rounded-full bg-green-600 hover:bg-green-700 text-white shadow-md transition-all duration-300 hover:scale-110 p-0"
            >
              <Gavel className="w-3 h-3" />
            </Button>
            <span className="text-white text-xs mt-0.5 font-medium">Bid</span>
          </div>

          {/* Bid count - smaller */}
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 rounded-full bg-black/40 backdrop-blur-sm flex items-center justify-center">
              <TrendingUp className="w-3 h-3 text-green-400" />
            </div>
            <span className="text-white text-xs mt-0.5 font-medium">
              {reel.bidCount}
            </span>
          </div>
        </div>

        {/* Bottom content overlay - compact */}
        <div className="absolute bottom-0 left-0 right-0 p-2 z-10">
          <div className="space-y-1.5">
            {/* Title - smaller */}
            <h3 className="font-semibold text-sm text-white line-clamp-2 drop-shadow-lg leading-tight">
              {reel.title}
            </h3>

            {/* Auction info - compact */}
            <div className="flex items-end justify-between">
              <div className="flex-1 min-w-0">
                <div className="text-xs text-gray-300">Current Bid</div>
                <div className="text-base font-bold text-green-400 drop-shadow-lg">
                  {formatCurrency(reel.product.currentBid)}
                </div>
              </div>

              <div className="text-right text-xs mr-10">
                <div className="flex items-center gap-1 text-white">
                  <Clock className="w-3 h-3" />
                  <span className="font-medium">
                    {formatTimeRemaining(reel.endTime)}
                  </span>
                </div>
              </div>
            </div>

            {/* Auctioneer info - minimal */}
            {/* <div className="flex items-center gap-1.5 pt-1">
              <img
                src={reel.auctioneer.avatar}
                alt={reel.auctioneer.name}
                className="w-4 h-4 rounded-full border border-white/20"
              />
              <span className="text-white text-xs font-medium truncate">
                {reel.auctioneer.name.split(' ')[0]}
              </span>
              <div className="flex items-center gap-0.5">
                <span className="text-yellow-400 text-xs">★</span>
                <span className="text-white text-xs">{reel.auctioneer.rating}</span>
              </div>
            </div> */}
          </div>
        </div>
      </div>
    </Card>
  );
}
